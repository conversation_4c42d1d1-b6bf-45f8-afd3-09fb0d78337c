#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度移动端相关搜索词挖掘工具（GUI版本）
更新日期：2025-7-28 添加ttkbootstrap美化界面
"""
import re
import random
import time
import threading
from queue import Queue
from threading import Thread
import requests
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import os
from datetime import datetime
import configparser

class ProxyPool:
    """代理池管理类"""
    def __init__(self):
        self.proxies = []
        self.failed_proxies = set()
        self.current_index = 0
        self.lock = threading.Lock()

    def fetch_proxies(self):
        """从神龙IP获取代理列表"""
        url = "http://api.shenlongip.com/ip"
        params = {
            'key': 'pbnw4imi',
            'protocol': '1',
            'mr': '1',
            'pattern': 'txt',
            'count': '400',
            'sign': 'c1355a34c52f0285cb22296923859071'
        }

        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
        }
        if os.path.exists('./proxy.txt'):
            with open('./proxy.txt', 'r') as f:
                lines = f.readlines()
                proxy_list = [line.strip() for line in lines if line.strip()]
                with self.lock:
                    self.proxies = proxy_list
                    self.failed_proxies.clear()
                    self.current_index = 0
                print(f"成功读取 {len(proxy_list)} 个代理IP")
                return True
        try:
            response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
            response.raise_for_status()

            # 解析代理列表
            proxy_list = []
            for line in response.text.strip().split('\n'):
                line = line.strip()
                if line and ':' in line:
                    proxy_list.append(line)

            with self.lock:
                self.proxies = proxy_list
                self.failed_proxies.clear()
                self.current_index = 0

            print(f"成功获取 {len(proxy_list)} 个代理IP")
            if not os.path.exists('./proxy.txt'):
                with open('./proxy.txt', 'a+') as file:
                    file.write("\n".join(proxy_list))
            return len(proxy_list) > 0

        except Exception as e:
            print(f"获取代理失败: {e}")
            return False

    def get_proxy(self):
        """获取一个可用的代理"""
        with self.lock:
            if not self.proxies:
                return None

            # 过滤掉失败的代理
            available_proxies = [p for p in self.proxies if p not in self.failed_proxies]

            if not available_proxies:
                # 如果所有代理都失败了，重新获取
                print("所有代理都已失效，尝试重新获取...")
                if self.fetch_proxies():
                    available_proxies = self.proxies
                else:
                    return None

            if available_proxies:
                # 轮询选择代理
                proxy = available_proxies[self.current_index % len(available_proxies)]
                self.current_index += 1
                return proxy

            return None

    def mark_failed(self, proxy):
        """标记代理为失败"""
        with self.lock:
            self.failed_proxies.add(proxy)
            print(f"代理失效: {proxy}")

    def get_proxy_dict(self, proxy):
        """将代理字符串转换为requests可用的代理字典"""
        if not proxy:
            return None

        proxy_url = f"http://{proxy}"
        return {
            'http': proxy_url,
            'https': proxy_url
        }

class BaiduMobileSpider(Thread):
    result = set()          # 关键词集合
    seen = set()            # 已处理关键词记录
    lock = threading.Lock() # 线程安全锁
    stop_flag = False       # 停止标志
    proxy_pool = None       # 代理池实例

    @classmethod
    def load_config(cls):
        """从配置文件加载设置"""
        try:
            # 使用RawConfigParser避免%字符的插值问题
            config = configparser.RawConfigParser()
            config.read('config.ini', encoding='utf-8')
            cookie = config.get('COOKIE', 'cookie', fallback='')
            print("读取配置文件成功",cookie)
            return cookie
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return ''

    @classmethod
    def get_headers(cls):
        """获取请求头（增强反检测）"""
        cookie = cls.load_config()

        # 随机选择User-Agent
        user_agents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Mobile Safari/537.36',
            'Mozilla/5.0 (Linux; Android 10; MI 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.93 Mobile Safari/537.36'
        ]

        import random
        selected_ua = random.choice(user_agents)

        return {
            'User-Agent': selected_ua,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cookie': cookie,
            'Referer': 'https://www.baidu.com/'
        }

    def __init__(self, task_queue, max_depth, failed_log, gui_callback=None):
        super().__init__()
        self.task_queue = task_queue
        self.max_depth = max_depth
        self.failed_log = failed_log
        self.gui_callback = gui_callback  # GUI回调函数
        self.headers = self.get_headers()  # 动态获取请求头

    def run(self):
        while not self.stop_flag:
            try:
                # 使用超时获取任务，以便能够响应停止信号
                keyword, current_depth = self.task_queue.get(timeout=1)
                try:
                    if not self.stop_flag:  # 再次检查停止标志
                        self.process_keyword(keyword, current_depth)
                except Exception as e:
                    print(f"处理异常 {keyword}: {str(e)}")
                finally:
                    self.task_queue.task_done()
            except:
                # 队列为空或超时，继续循环检查停止标志
                continue

    def process_keyword(self, keyword, current_depth):
        """处理单个关键词"""
        # 检查停止标志
        if self.stop_flag:
            return

        status_msg = f"[深度 {current_depth}] 正在采集: {keyword}"
        print(status_msg)

        # 更新GUI状态
        if self.gui_callback:
            self.gui_callback('status', status_msg)

        # 再次检查停止标志
        if self.stop_flag:
            return

        suggestions = self.download_page(keyword)

        # 检查停止标志
        if self.stop_flag:
            return

        if not suggestions:
            with self.lock:  # 线程安全写入
                self.failed_log.write(f"{keyword}\n")
            return

        keywords = self.process_suggestions(suggestions)

        # 检查停止标志
        if self.stop_flag:
            return

        self.update_results(keywords, current_depth)

    def download_page(self, keyword):
        """使用百度建议API获取相关关键词（替代页面抓取）"""
        # 检查停止标志
        if self.stop_flag:
            return None

        # 使用百度移动端建议API
        url = "https://m.baidu.com/su"
        params = {
            'wd': keyword,
            'action': 'opensearch',
            'ie': 'utf-8',
            'from': 'result',
            't': str(int(time.time() * 1000))
        }

        max_retries = 3  # 最大重试次数

        for attempt in range(max_retries):
            if self.stop_flag:
                return None

            # 获取代理
            proxy = None
            proxies = None
            if self.proxy_pool:
                proxy = self.proxy_pool.get_proxy()
                if proxy:
                    proxies = self.proxy_pool.get_proxy_dict(proxy)

            try:
                # 随机延迟，模拟人类行为
                delay = random.uniform(1.0, 3.0)
                for _ in range(int(delay * 10)):  # 分成0.1秒的小段
                    if self.stop_flag:
                        return None
                    time.sleep(0.1)

                if self.stop_flag:
                    return None

                # 更新请求头（每次请求都重新生成）
                headers = self.get_headers()

                # 发送请求
                response = requests.get(
                    url,
                    params=params,
                    headers=headers,
                    proxies=proxies,
                    timeout=15,
                    verify=False  # 忽略SSL证书验证
                )
                response.raise_for_status()

                # 解析JSON响应
                try:
                    data = response.json()
                    if isinstance(data, list) and len(data) > 1 and isinstance(data[1], list):
                        suggestions = data[1]
                        if suggestions:
                            # 请求成功，返回建议列表
                            if proxy:
                                print(f"使用代理 {proxy} 成功获取: {keyword} ({len(suggestions)} 个建议)")
                            else:
                                print(f"直连成功获取: {keyword} ({len(suggestions)} 个建议)")
                            return suggestions
                        else:
                            print(f"关键词 {keyword} 没有找到建议")
                            return []
                    else:
                        print(f"API响应格式异常: {keyword}")
                        return []

                except Exception as json_error:
                    print(f"JSON解析失败: {keyword} - {json_error}")
                    return []

            except Exception as e:
                error_msg = f"API请求失败 (尝试 {attempt + 1}/{max_retries}): {keyword}"
                if proxy:
                    error_msg += f" [代理: {proxy}]"
                    # 标记代理失效
                    if self.proxy_pool:
                        self.proxy_pool.mark_failed(proxy)
                error_msg += f" - {str(e)}"
                print(error_msg)

                # 如果是最后一次尝试，返回空列表
                if attempt == max_retries - 1:
                    return []

                # 短暂等待后重试
                time.sleep(random.uniform(1, 3))

        return []

    def process_suggestions(self, suggestions):
        """处理百度建议API返回的关键词列表"""
        if not suggestions:
            return []

        processed_keywords = []

        for suggestion in suggestions:
            if self.stop_flag:
                break

            cleaned = self.clean_text(suggestion)
            if cleaned and len(cleaned) >= 2 and len(cleaned) <= 50:
                # 过滤掉明显不是关键词的内容
                if not re.match(r'^[\d\s\-_=+<>()（）【】\[\]]+$', cleaned):
                    processed_keywords.append(cleaned)

        # 添加调试信息
        if processed_keywords:
            print(f"处理得到 {len(processed_keywords)} 个有效关键词")

        return processed_keywords

    def extract_keywords(self, html):
        """优化正则匹配相关搜索词"""
        if not html:
            return []

        # 多种正则模式，适应不同的页面结构
        patterns = [
            # 原有模式
            r'<span\s+class="[^"]*?(?:content-link_\w+|c-fwb)[^"]*?">(.*?)</span>',
            # 新增模式 - 相关搜索
            r'<a[^>]*?class="[^"]*?rela[^"]*?"[^>]*?>(.*?)</a>',
            # 新增模式 - 搜索建议
            r'<div[^>]*?class="[^"]*?suggest[^"]*?"[^>]*?>(.*?)</div>',
            # 新增模式 - 通用链接
            r'<a[^>]*?href="[^"]*?word=[^"]*?"[^>]*?>(.*?)</a>',
            # 新增模式 - 搜索词高亮
            r'<em[^>]*?>(.*?)</em>',
            # 新增模式 - 简单文本提取
            r'data-query="([^"]+)"',
            # 新增模式 - 更宽泛的span标签
            r'<span[^>]*?>([\u4e00-\u9fa5\w\s]{2,20})</span>'
        ]

        all_keywords = set()

        for pattern_str in patterns:
            try:
                pattern = re.compile(pattern_str, re.S|re.I)
                matches = pattern.findall(html)
                for match in matches:
                    cleaned = self.clean_text(match)
                    if cleaned and len(cleaned) >= 2 and len(cleaned) <= 20:
                        # 过滤掉明显不是关键词的内容
                        if not re.match(r'^[\d\s\-_=+<>()（）【】\[\]]+$', cleaned):
                            all_keywords.add(cleaned)
            except Exception as e:
                print(f"正则匹配错误: {pattern_str} - {e}")
                continue

        keywords_list = list(all_keywords)

        # 添加调试信息
        if keywords_list:
            print(f"提取到 {len(keywords_list)} 个关键词: {keywords_list[:5]}...")
        else:
            print("未提取到关键词，保存HTML片段用于调试...")
            # 保存HTML片段用于调试
            try:
                with open('debug_html.txt', 'w', encoding='utf-8') as f:
                    f.write(f"=== 调试HTML片段 ===\n")
                    f.write(html[:2000])  # 只保存前2000字符
                    f.write(f"\n=== 结束 ===\n")
            except:
                pass

        return keywords_list

    def clean_text(self, text):
        """净化文本内容"""
        if not text:
            return ""

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 移除特殊字符和多余空白
        text = re.sub(r'[\r\n\t]+', ' ', text)
        text = re.sub(r'\s+', ' ', text)

        # 移除首尾空白
        text = text.strip()

        # 移除常见的无用词汇
        useless_words = ['更多', '相关', '搜索', '百度', '为您推荐', '热门', '最新', '查看', '点击', '进入']
        for word in useless_words:
            if text == word:
                return ""

        return text

    def update_results(self, new_keywords, current_depth):
        """更新结果集合并添加新任务"""
        # 检查停止标志
        if self.stop_flag:
            return

        with BaiduMobileSpider.lock:  # 线程安全操作
            new_count = 0
            for kw in new_keywords:
                # 检查停止标志
                if self.stop_flag:
                    break

                if not kw:
                    continue

                # 添加关键词到结果集
                if kw not in self.result:
                    self.result.add(kw)
                    new_count += 1

                # 添加新任务到队列（只有在未停止时才添加）
                if not self.stop_flag and current_depth < self.max_depth and kw not in self.seen:
                    self.seen.add(kw)
                    self.task_queue.put((kw, current_depth + 1))

            # 更新GUI结果计数
            if self.gui_callback and new_count > 0 and not self.stop_flag:
                self.gui_callback('result_count', len(self.result))

class KeywordMinerGUI:
    def __init__(self):
        # 创建主窗口
        self.root = ttk_bs.Window(themename="superhero")  # 使用深色主题
        self.root.title("百度移动端关键词挖掘工具 v2.0")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # 配置参数
        self.config = {
            'thread_num': 8,
            'max_depth': 3,
            'seed_file': 'keywords.txt',
            'output_file': 'results.txt',
            'failed_file': 'failed.log',
            'use_proxy': True  # 是否使用代理
        }

        # 运行状态
        self.is_running = False
        self.threads = []
        self.task_queue = None
        self.failed_log = None

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk_bs.Frame(self.root, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # 标题
        title_label = ttk_bs.Label(
            main_frame,
            text="百度移动端关键词挖掘工具",
            font=("Microsoft YaHei", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # 配置区域
        self.setup_config_frame(main_frame)

        # 控制按钮区域
        self.setup_control_frame(main_frame)

        # 状态显示区域
        self.setup_status_frame(main_frame)

        # 结果显示区域
        self.setup_result_frame(main_frame)

    def setup_config_frame(self, parent):
        """设置配置区域"""
        config_frame = ttk_bs.LabelFrame(parent, text="配置参数", padding=15, bootstyle="info")
        config_frame.pack(fill=X, pady=(0, 15))

        # 第一行：线程数和深度
        row1 = ttk_bs.Frame(config_frame)
        row1.pack(fill=X, pady=(0, 10))

        ttk_bs.Label(row1, text="并发线程数:", width=12).pack(side=LEFT)
        self.thread_var = ttk_bs.IntVar(value=self.config['thread_num'])
        thread_spin = ttk_bs.Spinbox(row1, from_=1, to=20, textvariable=self.thread_var, width=10)
        thread_spin.pack(side=LEFT, padx=(5, 20))

        ttk_bs.Label(row1, text="挖掘深度:", width=10).pack(side=LEFT)
        self.depth_var = ttk_bs.IntVar(value=self.config['max_depth'])
        depth_spin = ttk_bs.Spinbox(row1, from_=1, to=10, textvariable=self.depth_var, width=10)
        depth_spin.pack(side=LEFT, padx=5)

        # 第二行：文件路径
        row2 = ttk_bs.Frame(config_frame)
        row2.pack(fill=X, pady=(0, 10))

        ttk_bs.Label(row2, text="种子文件:", width=12).pack(side=LEFT)
        self.seed_file_var = ttk_bs.StringVar(value=self.config['seed_file'])
        seed_entry = ttk_bs.Entry(row2, textvariable=self.seed_file_var, width=30)
        seed_entry.pack(side=LEFT, padx=5)

        ttk_bs.Button(
            row2, text="浏览",
            command=self.browse_seed_file,
            bootstyle="outline-secondary",
            width=8
        ).pack(side=LEFT, padx=5)

        # 第三行：输出文件
        row3 = ttk_bs.Frame(config_frame)
        row3.pack(fill=X, pady=(0, 10))

        ttk_bs.Label(row3, text="输出文件:", width=12).pack(side=LEFT)
        self.output_file_var = ttk_bs.StringVar(value=self.config['output_file'])
        output_entry = ttk_bs.Entry(row3, textvariable=self.output_file_var, width=30)
        output_entry.pack(side=LEFT, padx=5)

        ttk_bs.Button(
            row3, text="浏览",
            command=self.browse_output_file,
            bootstyle="outline-secondary",
            width=8
        ).pack(side=LEFT, padx=5)

        # 第四行：代理设置
        row4 = ttk_bs.Frame(config_frame)
        row4.pack(fill=X)

        self.use_proxy_var = ttk_bs.BooleanVar(value=self.config['use_proxy'])
        proxy_check = ttk_bs.Checkbutton(
            row4,
            text="使用代理池",
            variable=self.use_proxy_var,
            bootstyle="success-round-toggle"
        )
        proxy_check.pack(side=LEFT, padx=(0, 20))

        self.proxy_status_var = ttk_bs.StringVar(value="未初始化")
        ttk_bs.Label(row4, text="代理状态:", width=10).pack(side=LEFT)
        self.proxy_status_label = ttk_bs.Label(row4, textvariable=self.proxy_status_var, bootstyle="info")
        self.proxy_status_label.pack(side=LEFT, padx=5)

        ttk_bs.Button(
            row4, text="获取代理",
            command=self.fetch_proxies,
            bootstyle="outline-info",
            width=10
        ).pack(side=LEFT, padx=(20, 0))

    def setup_control_frame(self, parent):
        """设置控制按钮区域"""
        control_frame = ttk_bs.Frame(parent)
        control_frame.pack(fill=X, pady=(0, 15))

        self.start_btn = ttk_bs.Button(
            control_frame,
            text="开始挖掘",
            command=self.start_mining,
            bootstyle="success",
            width=15
        )
        self.start_btn.pack(side=LEFT, padx=(0, 10))

        self.stop_btn = ttk_bs.Button(
            control_frame,
            text="停止挖掘",
            command=self.stop_mining,
            bootstyle="danger",
            width=15,
            state=DISABLED
        )
        self.stop_btn.pack(side=LEFT, padx=(0, 10))

        self.save_btn = ttk_bs.Button(
            control_frame,
            text="保存结果",
            command=self.save_results,
            bootstyle="info",
            width=15
        )
        self.save_btn.pack(side=LEFT, padx=(0, 10))

        self.clear_btn = ttk_bs.Button(
            control_frame,
            text="清空结果",
            command=self.clear_results,
            bootstyle="warning",
            width=15
        )
        self.clear_btn.pack(side=LEFT)

    def setup_status_frame(self, parent):
        """设置状态显示区域"""
        status_frame = ttk_bs.LabelFrame(parent, text="运行状态", padding=10, bootstyle="secondary")
        status_frame.pack(fill=X, pady=(0, 15))

        # 状态信息
        info_frame = ttk_bs.Frame(status_frame)
        info_frame.pack(fill=X, pady=(0, 10))

        ttk_bs.Label(info_frame, text="当前状态:", width=12).pack(side=LEFT)
        self.status_var = ttk_bs.StringVar(value="就绪")
        self.status_label = ttk_bs.Label(info_frame, textvariable=self.status_var, bootstyle="info")
        self.status_label.pack(side=LEFT, padx=(5, 20))

        ttk_bs.Label(info_frame, text="已挖掘:", width=10).pack(side=LEFT)
        self.count_var = ttk_bs.StringVar(value="0")
        self.count_label = ttk_bs.Label(info_frame, textvariable=self.count_var, bootstyle="success")
        self.count_label.pack(side=LEFT, padx=5)

        # 进度条
        self.progress = ttk_bs.Progressbar(
            status_frame,
            mode='indeterminate',
            bootstyle="info-striped"
        )
        self.progress.pack(fill=X, pady=(5, 0))

    def setup_result_frame(self, parent):
        """设置结果显示区域"""
        result_frame = ttk_bs.LabelFrame(parent, text="挖掘结果", padding=10, bootstyle="success")
        result_frame.pack(fill=BOTH, expand=True)

        # 创建文本框和滚动条
        text_frame = ttk_bs.Frame(result_frame)
        text_frame.pack(fill=BOTH, expand=True)

        self.result_text = scrolledtext.ScrolledText(
            text_frame,
            wrap=tk.WORD,
            font=("Consolas", 10),
            bg="#2b3e50",
            fg="#ecf0f1",
            insertbackground="#ecf0f1",
            selectbackground="#3498db"
        )
        self.result_text.pack(fill=BOTH, expand=True)

    def browse_seed_file(self):
        """浏览种子文件"""
        filename = filedialog.askopenfilename(
            title="选择种子关键词文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.seed_file_var.set(filename)

    def browse_output_file(self):
        """浏览输出文件"""
        filename = filedialog.asksaveasfilename(
            title="选择输出文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_file_var.set(filename)

    def fetch_proxies(self):
        """获取代理列表"""
        def _fetch():
            try:
                self.proxy_status_var.set("正在获取代理...")

                # 初始化代理池
                if not hasattr(self, 'proxy_pool') or not self.proxy_pool:
                    self.proxy_pool = ProxyPool()

                # 获取代理
                success = self.proxy_pool.fetch_proxies()

                if success:
                    count = len(self.proxy_pool.proxies)
                    self.proxy_status_var.set(f"已获取 {count} 个代理")
                    messagebox.showinfo("成功", f"成功获取 {count} 个代理IP")
                else:
                    self.proxy_status_var.set("获取失败")
                    messagebox.showerror("错误", "获取代理失败，请检查网络连接")

            except Exception as e:
                self.proxy_status_var.set("获取失败")
                messagebox.showerror("错误", f"获取代理时出错: {str(e)}")

        # 在后台线程中执行
        Thread(target=_fetch, daemon=True).start()

    def gui_callback(self, callback_type, data):
        """GUI回调函数"""
        if callback_type == 'status':
            self.root.after(0, lambda: self.status_var.set(data))
        elif callback_type == 'result_count':
            self.root.after(0, lambda: self.count_var.set(str(data)))

    def start_mining(self):
        """开始挖掘"""
        if self.is_running:
            return

        # 检查种子文件
        seed_file = self.seed_file_var.get()
        if not os.path.exists(seed_file):
            messagebox.showerror("错误", f"种子文件不存在: {seed_file}")
            return

        # 更新配置
        self.config['thread_num'] = self.thread_var.get()
        self.config['max_depth'] = self.depth_var.get()
        self.config['seed_file'] = seed_file
        self.config['output_file'] = self.output_file_var.get()
        self.config['use_proxy'] = self.use_proxy_var.get()

        # 清空之前的结果和重置停止标志
        BaiduMobileSpider.result.clear()
        BaiduMobileSpider.seen.clear()
        BaiduMobileSpider.stop_flag = False  # 重置停止标志

        # 初始化代理池
        if self.config['use_proxy']:
            if not hasattr(self, 'proxy_pool') or not self.proxy_pool:
                self.proxy_pool = ProxyPool()
                # 自动获取代理
                if not self.proxy_pool.fetch_proxies():
                    if messagebox.askyesno("代理获取失败", "无法获取代理IP，是否继续不使用代理？"):
                        self.config['use_proxy'] = False
                        self.proxy_pool = None
                    else:
                        return
            BaiduMobileSpider.proxy_pool = self.proxy_pool
        else:
            BaiduMobileSpider.proxy_pool = None

        # 启动挖掘线程
        mining_thread = Thread(target=self._mining_worker, daemon=True)
        mining_thread.start()

        # 更新UI状态
        self.is_running = True
        self.start_btn.config(state=DISABLED)
        self.stop_btn.config(state=NORMAL)
        self.progress.start()
        self.status_var.set("正在挖掘...")

    def _mining_worker(self):
        """挖掘工作线程"""
        try:
            # 初始化任务队列
            self.task_queue = Queue()

            # 读取种子关键词
            with open(self.config['seed_file'], 'r', encoding='utf-8') as f:
                for line in f:
                    if BaiduMobileSpider.stop_flag:  # 检查停止标志
                        break
                    seed = line.strip()
                    if seed:
                        self.task_queue.put((seed, 1))
                        BaiduMobileSpider.seen.add(seed)

            # 如果已经停止，直接返回
            if BaiduMobileSpider.stop_flag:
                return

            # 启动工作线程
            with open(self.config['failed_file'], 'w', encoding='utf-8') as failed_log:
                self.failed_log = failed_log
                self.threads = []

                for _ in range(self.config['thread_num']):
                    if BaiduMobileSpider.stop_flag:  # 检查停止标志
                        break
                    spider = BaiduMobileSpider(
                        self.task_queue,
                        self.config['max_depth'],
                        failed_log,
                        self.gui_callback
                    )
                    spider.daemon = True
                    spider.start()
                    self.threads.append(spider)

                # 等待所有任务完成或停止信号
                while not BaiduMobileSpider.stop_flag and not self.task_queue.empty():
                    time.sleep(0.1)  # 短暂休眠，避免占用过多CPU

                # 如果是正常完成（非停止），等待队列完成
                if not BaiduMobileSpider.stop_flag:
                    self.task_queue.join()

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"挖掘过程中出现错误: {str(e)}"))
        finally:
            # 挖掘完成，更新UI
            self.root.after(0, self._mining_finished)

    def _mining_finished(self):
        """挖掘完成后的处理"""
        self.is_running = False
        self.start_btn.config(state=NORMAL)
        self.stop_btn.config(state=DISABLED)
        self.progress.stop()

        result_count = len(BaiduMobileSpider.result)

        # 根据是否是停止状态显示不同的消息
        if BaiduMobileSpider.stop_flag:
            self.status_var.set(f"挖掘已停止！共获得 {result_count} 个关键词")
        else:
            self.status_var.set(f"挖掘完成！共获得 {result_count} 个关键词")

        self.count_var.set(str(result_count))

        # 显示结果
        self.display_results()

        # 自动保存结果（如果有结果的话）
        if result_count > 0:
            self.save_results()

    def display_results(self):
        """显示挖掘结果"""
        self.result_text.delete(1.0, tk.END)

        if BaiduMobileSpider.result:
            sorted_results = sorted(BaiduMobileSpider.result)
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            header = f"=== 挖掘结果 ({len(sorted_results)} 个关键词) - {timestamp} ===\n\n"
            self.result_text.insert(tk.END, header)

            for i, keyword in enumerate(sorted_results, 1):
                self.result_text.insert(tk.END, f"{i:4d}. {keyword}\n")
        else:
            self.result_text.insert(tk.END, "暂无挖掘结果")

        # 滚动到顶部
        self.result_text.see(1.0)

    def stop_mining(self):
        """停止挖掘"""
        if not self.is_running:
            return

        # 设置停止标志，立即停止所有线程
        BaiduMobileSpider.stop_flag = True
        self.is_running = False

        # 更新UI状态
        self.start_btn.config(state=NORMAL)
        self.stop_btn.config(state=DISABLED)
        self.progress.stop()
        self.status_var.set("正在停止挖掘...")

        # 启动一个线程来等待所有工作线程结束
        stop_thread = Thread(target=self._wait_for_stop, daemon=True)
        stop_thread.start()

    def _wait_for_stop(self):
        """等待所有工作线程停止"""
        try:
            # 等待所有线程结束（最多等待5秒）
            for thread in self.threads:
                if thread.is_alive():
                    thread.join(timeout=5)

            # 清空任务队列中剩余的任务
            while not self.task_queue.empty():
                try:
                    self.task_queue.get_nowait()
                    self.task_queue.task_done()
                except:
                    break

        except Exception as e:
            print(f"停止线程时出错: {e}")
        finally:
            # 更新UI状态
            self.root.after(0, lambda: self.status_var.set("已停止挖掘"))

    def save_results(self):
        """保存结果到文件"""
        if not BaiduMobileSpider.result:
            messagebox.showwarning("警告", "没有可保存的结果")
            return

        try:
            output_file = self.output_file_var.get()
            with open(output_file, 'w', encoding='utf-8') as f:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"# 百度移动端关键词挖掘结果\n")
                f.write(f"# 生成时间: {timestamp}\n")
                f.write(f"# 关键词数量: {len(BaiduMobileSpider.result)}\n\n")

                for keyword in sorted(BaiduMobileSpider.result):
                    f.write(f"{keyword}\n")

            messagebox.showinfo("成功", f"结果已保存到: {output_file}")

        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

    def clear_results(self):
        """清空结果"""
        if messagebox.askyesno("确认", "确定要清空所有结果吗？"):
            BaiduMobileSpider.result.clear()
            BaiduMobileSpider.seen.clear()
            self.result_text.delete(1.0, tk.END)
            self.count_var.set("0")
            self.status_var.set("已清空结果")

def main():
    """命令行版本的主函数"""
    # 配置参数
    CONFIG = {
        'thread_num': 8,       # 并发线程数
        'max_depth': 3,        # 挖掘深度
        'seed_file': 'keywords.txt',  # 初始关键词文件
        'output_file': 'results.txt',  # 输出文件改为txt
        'failed_file': 'failed.log',    # 失败记录
        'use_proxy': True      # 是否使用代理
    }

    # 初始化代理池
    if CONFIG['use_proxy']:
        print("正在初始化代理池...")
        proxy_pool = ProxyPool()
        if proxy_pool.fetch_proxies():
            BaiduMobileSpider.proxy_pool = proxy_pool
            print(f"代理池初始化成功，共 {len(proxy_pool.proxies)} 个代理")
        else:
            print("代理池初始化失败，将不使用代理")
            BaiduMobileSpider.proxy_pool = None
    else:
        BaiduMobileSpider.proxy_pool = None

    # 初始化任务队列
    task_queue = Queue()
    with open(CONFIG['seed_file'], 'r', encoding='utf-8') as f:
        for line in f:
            seed = line.strip()
            if seed:
                task_queue.put((seed, 1))
                BaiduMobileSpider.seen.add(seed)

    # 启动工作线程
    with open(CONFIG['failed_file'], 'w', encoding='utf-8') as failed_log:
        # 创建并启动线程
        threads = []
        for _ in range(CONFIG['thread_num']):
            spider = BaiduMobileSpider(task_queue, CONFIG['max_depth'], failed_log)
            spider.daemon = True
            spider.start()
            threads.append(spider)

        # 等待所有任务完成
        task_queue.join()

    # 保存结果到txt文件
    with open(CONFIG['output_file'], 'w', encoding='utf-8') as f:
        # 按字母顺序排序后写入
        for keyword in sorted(BaiduMobileSpider.result):
            f.write(f"{keyword}\n")

    print(f"任务完成！共挖掘到 {len(BaiduMobileSpider.result)} 个唯一关键词")

if __name__ == '__main__':
    # 检查是否有命令行参数，如果有则使用命令行模式
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == '--cli':
        main()
    else:
        # 启动GUI
        app = KeywordMinerGUI()
        app.root.mainloop()
